#!/usr/bin/env python3
# -*- coding: utf-8 -*-

SYSTEM_CLEANUP = {
    "清理系统临时文件": ["del /f /s /q %windir%\\Temp\\*.*", "rd /s /q %windir%\\Temp"],
    "清理系统临时目录": ["del /f /s /q %TEMP%\\*.*", "rd /s /q %TEMP%"],
    "清理预读取文件": ["del /f /s /q %windir%\\Prefetch\\*.*"],
    "清理用户临时文件": [
        "del /f /s /q %USERPROFILE%\\AppData\\Local\\Temp\\*.*",
        "rd /s /q %USERPROFILE%\\AppData\\Local\\Temp",
    ],
    "清理IE缓存": ["RunDll32.exe InetCpl.cpl,ClearMyTracksByProcess 255"],
    "清理Web缓存": ["del /f /s /q %LOCALAPPDATA%\\Microsoft\\Windows\\WebCache\\*.*"],
    "清理系统日志文件": ["del /f /s /q %windir%\\Logs\\*.*"],
    "清理系统回收站": ["rd /s /q C:\\$Recycle.Bin"],
    "运行系统磁盘清理": ["cleanmgr /sagerun:1"],
    "清理系统更新缓存": [
        "net stop wuauserv",
        "rd /s /q %windir%\\SoftwareDistribution",
        "net start wuauserv",
    ],
    "清理系统缩略图缓存": [
        "del /f /s /q %LOCALAPPDATA%\\Microsoft\\Windows\\Explorer\\thumbcache_*.db"
    ],
    "清理系统DNS缓存": ["ipconfig /flushdns"],
}
