### **项目需求：Windows硬件工具箱开发（PySide6 + PySide6-Fluent-Widgets）**

#### **1. 界面要求**
- **布局**：
  - 左侧导航栏，右侧内容区域
  - 支持全局主题切换（浅色/深色/跟随系统模式）
- **现代化设计**：
  - **必须要求**：必须使用PySide6-Fluent-Widgets组件库，除非PySide6-Fluent-Widgets组件库不存在需要的组件，才可以使用PySide6原生组件
  - 遵循Fluent Design2.0设计规范（参考文档：[Microsoft Fluent Design](https://learn.microsoft.com/zh-cn/windows/apps/design/)）。
  - PySide6-Fluent-Widgets组件库文档：https://github.com/zhiyiYo/PyQt-Fluent-Widgets/tree/PySide6
  - 除快捷工具需要分类，其他所有视图都不允许添加分类、搜索、收藏、刷新等功能。
  - 必须严格按需求开发，绝对禁止添加额外功能，例如回滚、日志等
  - 整个项目所有视图界面都不使用折叠，平铺式布局
  - 在开始前，你应该构思每个视图的布局方式
- **风格化图标**：
- **软件图标**：
  - 使用`assets\icon.ico`作为应用图标。
  - 在使用PySide6-Fluent-Widgets中FluentIcon提供的图标前，你必须通过Context7 MCP工具来获取所有可用的FluentIcon图标，避免使用不存在的FluentIcon图标
  - 除导航栏以外需要使用图标，视图界面禁止使用任何图标
- **启动页面**：
  - 必须以管理员方式运行，若未以管理员模式启动，弹出对话框提醒用户。并在所有涉及系统级操作前再次检测权限。
- **架构设计**
  - 应该遵循MVC设计模式
  - 不要滥用MVC，界面中的视图层(View)不能太过于复杂，否则将代码分散到其他模块，将降低程序的可维护性和可读性。
  - 必须按照当前项目结构树在项目根目录生成项目结构树


#### **2. 功能模块要求**
- **软件启动页面**
  -使用PySide6-Fluent-Widgets组件库的splashscreen组件编写启动页面，启动页面应与主窗口大小一致
- **硬件信息**：严格按照部分思路解析.md中的JSON字段获取硬件信息：
  - **系统信息**：caption, version, os_architecture, hostname
  - **主板信息**：manufacturer, product (主板) + manufacturer, version, release_date (BIOS)
  - **CPU信息**：device_id, name, manufacturer, current_clock_speed, max_clock_speed
  - **内存信息**：capacity, part_number, speed (每个物理插槽)
  - **显卡信息**：name, pnp_device_id, driver_version, current_refresh_rate, current_horizontal_resolution, current_vertical_resolution
  - **磁盘信息**：model, size (每个物理磁盘)
  - **必须注意**：异步获取硬件信息，在启动页面期间预加载
  - **界面要求**：
    - 硬件信息直观简洁，不允许使用折叠和滚动，平铺展示，需要复制硬件信息的按钮功能
    - 导出和复制应该显示硬件信息的中文名字，而不是获取到的硬件信息返回的json数据键的名字
    - **信息卡片使用骨架屏**：
      - 预加载信息时显示骨架屏动画
      - 固定卡片大小，避免布局跳动
      - 数据加载完成后替换为实际内容
- **优化清理**：
  - **必须注意**：异步执行所有优化清理操作，显示执行进度和状态反馈
  - **全局控制要求**：
    - 实现界面级全选复选框
    - 全选功能：一键选择/取消所有选项卡中的所有选项
    - 执行功能：支持一键执行所有任务或仅执行选中任务
  - **执行后操作**：重启文件资源管理器 + 删除 `iconcache.db` 文件
  - **选项卡分类**：
    1. **PowerShell设置**：必须使用复选框，以便实现全局控制要求。
       - 设置执行策略为`Bypass`（配置文件：`powershell_commands.py`中的`EXECUTION_POLICY_COMMAND`）。
       - 解锁电源高级选项（配置文件：`powershell_commands.py`中的`UNLOCK_POWER_COMMAND`）。
    2. **注册表优化**（配置文件：`registry_commands.py`）：必须使用复选框，以便实现全局控制要求。
       - 父子复选框联动规则：如父选中则所有子选中，子全选父自动选中，部分子选中父为半选。
       - 例如：任务栏相关设置为父，任务栏精确到秒为子。
       - 若任务包含多条注册表命令，只要有一条成功即视为执行成功。
    3. **系统清理**（配置文件：`system_cleanup.py`）：必须使用复选框，以便实现全局控制要求。
       - 清理系统临时文件、临时目录、预读取文件、用户临时文件、IE缓存、Web缓存、系统日志、回收站、更新缓存、缩略图缓存、DNS缓存。
       - 运行系统磁盘清理。

- **预装应用**：
  - **必须注意**：需要异步执行应用卸载操作，显示卸载进度和错误处理
  - **全局控制要求**：
    - 实现界面级全选复选框
    - 支持一键选择/取消所有选项卡中的所有选项
    - 支持一键执行所有任务或仅执行选中任务
    - **在用户执行卸载功能时**才进行应用包查找，逐个查找，每卸载一个查找一个，而不是全部同时查找
  - **选项卡分类**（配置文件：`appx_packages.py`，在执行卸载时使用通配符的方式在系统中查找对应的应用包名，然后使用查找的完整包名进行卸载预装应用包和卸载用户安装的应用包
）：必须使用复选框，以便实现全局控制要求。
    1. Windows Xbox相关应用。
    2. Windows Store商店应用。
    3. Windows音视频编解码器应用。
    4. Windows系统预装应用。
    5. 卸载并清理OneDrive（配置文件：`onedrive_cleanup.py`）：必须使用复选框，以便实现全局控制要求。
      - 停止OneDrive进程。
      - 卸载OneDrive UWP/传统应用。
      - 清理OneDrive文件夹、注册表项。
      - 禁用OneDrive文件资源管理器集成。
      - 清理OneDrive启动项。

- **超频工具**：
  - **必须注意**：新建子进程打开超频工具
  - 提供一键进入BIOS按钮（需用户二次确认）。
  - 扫描`OCTools`文件夹中的可执行程序。
    - 检查`OCTools`文件夹是否存在，不存在则创建。
    - 以文件夹名称作为工具名称，可执行路径为实际工具路径。
    - 提取可执行文件图标（基于部分思路解析.md中的IconExtractor类实现，并使用get_best_icon_pixmap获取最佳尺寸图标）。
    - 启动前检查可执行文件类型（GUI/控制台程序），以适合方式运行。
    - 优先使用与文件夹同名的可执行文件，否则查找其他可执行文件。
    - 所有工具均以管理员方式运行。
    - 统一卡片尺寸，保持界面整齐
    - 避免因内容长度不同导致的布局不一致

- **快捷工具**（配置文件：`quick_tools.py`）：
  - **必须注意**：使用新建子进程打开快捷工具
  - 根据提供的配置文件分类。
  - 以下工具需用户二次确认：
    - 安全模式（一次性操作，重启后恢复为正常模式）。
    - 重新启动、关机、睡眠、锁定计算机。
  - 其他工具点击按钮小卡片即可启动。
  

- **设置关于**：
  - **居中显示**：居中显示应用图标（assets/icon.ico）、名称、版本、版权信息
  - **主题配置**：深/浅/跟随系统。设置全局主题。界面无需设置颜色。
  - **检查更新**：直接跳转官网
  - **赞助作者**：
    - 自定义对话框内置选项卡组件
    - 显示支付宝二维码（assets/alipay.png）
    - 显示微信二维码（assets/wechat.png）
  - **抖音主页**：点击跳转作者抖音主页。
  - **官方Q群**：点击跳转QQ群。

#### **3. 配置文件要求**
    - 配置文件必须只引用提供的配置文件，不允许修改配置文件内容。

## **注意事项**
- **打包路径**：项目需要打包成单文件的可执行文件，并排除OCTools文件夹，注意开发环境和打包环境的路径问题。