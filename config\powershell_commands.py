#!/usr/bin/env python3
# -*- coding: utf-8 -*-

EXECUTION_POLICY_COMMAND = "Set-ExecutionPolicy Bypass -Scope LocalMachine -Force"
UNLOCK_POWER_COMMAND = "Get-ChildItem 'HKLM:\\SYSTEM\\CurrentControlSet\\Control\\Power\\PowerSettings' -Recurse | Where-Object {$_.PSPath -notmatch '\\\\DefaultPowerSchemeValues|(\\\\[0-9]|\\\\255)$'} | ForEach-Object {Set-ItemProperty -Path $_.PSPath -Name 'Attributes' -Value 2 -Force}"
